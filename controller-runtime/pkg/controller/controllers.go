package controllers

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

// PodReconciler 用于监听并处理 Pod 对象
type PodReconciler struct {
	client.Client
	Scheme *runtime.Scheme
}

// Reconcile 函数在每次侦测到与 Pod 相关的变更事件时被调用
func (r *PodReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// 1. 获取当前变动的 Pod
	var pod corev1.Pod
	if err := r.Get(ctx, req.NamespacedName, &pod); err != nil {
		// 如果是 not found，说明 Pod 被删除了，这里直接忽略即可
		// client.IgnoreNotFound 会在错误为 not found 时返回 nil，否则返回原错误
		return ctrl.Result{}, client.IgnoreNotFound(err)
	}

	// 2. 判断或处理业务逻辑
	//    示例：给所有 Pod 加上一个自定义 Label，表明它由该控制器管理（仅作演示）。
	//    真实场景可根据 Pod 的 Phase、Annotation、Label 等信息进行更灵活的逻辑处理。

	// 确保 Labels 非空
	if pod.Labels == nil {
		pod.Labels = make(map[string]string)
	}

	// 如果还没有 'controlled-by' 这个 Label，就加一个
	if pod.Labels["controlled-by"] != "my-controller" {
		logger.Info("Add label [controlled-by=my-controller] to Pod", "Pod", req.NamespacedName)

		pod.Labels["controlled-by"] = "my-controller"

		// 3. 更新 Pod
		//    注意更新 label 等 spec 之外的字段时，一般无需使用 .Status() 方式
		if err := r.Update(ctx, &pod); err != nil {
			logger.Error(err, "Failed to update Pod label", "Pod", req.NamespacedName)
			return ctrl.Result{}, err
		}
	} else {
		logger.Info("Pod already labeled by my-controller", "Pod", req.NamespacedName)
	}

	// 4. 返回结果
	//    ctrl.Result{} 表示无需再次立即重试
	//    如需定期检查，可返回 ctrl.Result{RequeueAfter: time.Second * 10} 之类的
	return ctrl.Result{}, nil
}

// SetupWithManager 将此控制器注册到 Manager，并声明要监听的资源类型
func (r *PodReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		// 告诉 controller-runtime：我们需要关注 Pod 对象
		For(&corev1.Pod{}).
		Complete(r)
}
